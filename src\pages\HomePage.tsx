import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faComment,
  faArrowRotateRight,
  faLayerGroup,
  faArrowsRotate,
  faSearch,
  faGrip,
  faList,
  faSort,
  faFolder,
  faInfoCircle,
  faLightbulb,
  faCode,
  faUsers,
  faFileCode,
  faImage,
  faXmark
} from '@fortawesome/free-solid-svg-icons'

interface ContextCard {
  id: string
  name: string
  category: string
  description: string
  chatCount: number
  fileCount: number
  lastUpdated: string
  color: 'primary' | 'secondary' | 'supplement2' | 'supplement1'
  icon: any
}

const HomePage: React.FC = () => {
  const navigate = useNavigate()
  const [searchQuery, setSearchQuery] = useState('')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [sortBy, setSortBy] = useState('Recent')
  const [selectedContext, setSelectedContext] = useState<ContextCard | null>(null)

  // Mock context data - will be replaced with real data from file system
  const contexts: ContextCard[] = [
    {
      id: 'project-alpha',
      name: 'Project Alpha',
      category: 'Design System',
      description: 'Modern design system with components, tokens, and documentation for the new product line.',
      chatCount: 12,
      fileCount: 24,
      lastUpdated: '2h ago',
      color: 'primary',
      icon: faFolder
    },
    {
      id: 'research-notes',
      name: 'Research Notes',
      category: 'User Research',
      description: 'User interview insights, behavioral patterns, and usability testing results compilation.',
      chatCount: 8,
      fileCount: 15,
      lastUpdated: '1d ago',
      color: 'secondary',
      icon: faLightbulb
    },
    {
      id: 'code-review',
      name: 'Code Review',
      category: 'Development',
      description: 'Code review discussions, pull request feedback, and technical documentation.',
      chatCount: 5,
      fileCount: 32,
      lastUpdated: '3d ago',
      color: 'supplement2',
      icon: faCode
    },
    {
      id: 'meeting-notes',
      name: 'Meeting Notes',
      category: 'Team Sync',
      description: 'Weekly team sync notes, action items, and project status updates.',
      chatCount: 3,
      fileCount: 7,
      lastUpdated: '5d ago',
      color: 'supplement1',
      icon: faUsers
    }
  ]

  const handleNewChat = () => {
    navigate('/chat')
  }

  const handleOrganize = () => {
    // TODO: Implement organize functionality
    console.log('Organize content')
  }

  const handleContextSelect = (context: ContextCard) => {
    setSelectedContext(context)
  }

  const handleContextView = (contextId: string) => {
    navigate(`/files/${contextId}`)
  }

  const closeContextDetails = () => {
    setSelectedContext(null)
  }

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'primary':
        return {
          gradient: 'from-primary/20 to-supplement2/20',
          border: 'border-primary/30 hover:border-primary/50',
          text: 'text-primary',
          bg: 'bg-primary',
          button: 'bg-primary text-gray-900 hover:bg-primary/80',
          dot: 'bg-primary'
        }
      case 'secondary':
        return {
          gradient: 'from-secondary/20 to-supplement1/20',
          border: 'border-secondary/30 hover:border-secondary/50',
          text: 'text-secondary',
          bg: 'bg-secondary',
          button: 'bg-secondary text-white hover:bg-secondary/80',
          dot: 'bg-secondary'
        }
      case 'supplement2':
        return {
          gradient: 'from-supplement2/20 to-tertiary/20',
          border: 'border-supplement2/30 hover:border-supplement2/50',
          text: 'text-supplement2',
          bg: 'bg-supplement2',
          button: 'bg-supplement2 text-gray-900 hover:bg-supplement2/80',
          dot: 'bg-supplement2'
        }
      case 'supplement1':
        return {
          gradient: 'from-supplement1/20 to-primary/20',
          border: 'border-supplement1/30 hover:border-supplement1/50',
          text: 'text-supplement1',
          bg: 'bg-supplement1',
          button: 'bg-supplement1 text-gray-900 hover:bg-supplement1/80',
          dot: 'bg-supplement1'
        }
      default:
        return {
          gradient: 'from-primary/20 to-supplement2/20',
          border: 'border-primary/30 hover:border-primary/50',
          text: 'text-primary',
          bg: 'bg-primary',
          button: 'bg-primary text-gray-900 hover:bg-primary/80',
          dot: 'bg-primary'
        }
    }
  }

  return (
    <div className="flex-1 flex flex-col bg-gray-900">
      {/* Search and Controls Header */}
      <div className="p-6 border-b border-tertiary/50">
        {/* Hero Section */}
        <div className="mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Start New Chat */}
            <div className="bg-gradient-to-br from-primary/20 to-supplement2/20 border border-primary/30 rounded-xl p-6 hover:border-primary/50 transition-all">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                  <FontAwesomeIcon icon={faComment} className="text-gray-900" />
                </div>
                <h3 className="text-lg font-semibold text-primary">Start a New Chat</h3>
              </div>
              <p className="text-sm text-supplement1 mb-4 leading-relaxed">
                Something new to work on? Let's start from chatting with 300+ AI models first. Co-create your new content and then enjoy the smart context vault experience.
              </p>
              <button 
                onClick={handleNewChat}
                className="flex items-center gap-2 px-4 py-2 bg-primary text-gray-900 rounded-lg font-medium hover:bg-primary/80 transition-colors"
              >
                <FontAwesomeIcon icon={faComment} className="text-sm" />
                <span>New Chat</span>
              </button>
            </div>

            {/* Continue Thoughts */}
            <div className="bg-gradient-to-br from-secondary/20 to-supplement1/20 border border-secondary/30 rounded-xl p-6 hover:border-secondary/50 transition-all">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-10 h-10 bg-secondary rounded-lg flex items-center justify-center">
                  <FontAwesomeIcon icon={faArrowRotateRight} className="text-white" />
                </div>
                <h3 className="text-lg font-semibold text-secondary">Continue your Thoughts</h3>
              </div>
              <p className="text-sm text-supplement1 mb-4 leading-relaxed">
                Find or select the recent context below to continue your masterpiece!
              </p>
              <div className="h-10"></div> {/* Spacer to align with other cards */}
            </div>

            {/* Organize Content */}
            <div className="bg-gradient-to-br from-supplement2/20 to-tertiary/20 border border-supplement2/30 rounded-xl p-6 hover:border-supplement2/50 transition-all">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-10 h-10 bg-supplement2 rounded-lg flex items-center justify-center">
                  <FontAwesomeIcon icon={faLayerGroup} className="text-gray-900" />
                </div>
                <h3 className="text-lg font-semibold text-supplement2">Organize the Content</h3>
              </div>
              <p className="text-sm text-supplement1 mb-4 leading-relaxed">
                Let your local AI model to organize the thoughts in the file vaults
              </p>
              <button 
                onClick={handleOrganize}
                className="flex items-center gap-2 px-4 py-2 bg-supplement2 text-gray-900 rounded-lg font-medium hover:bg-supplement2/80 transition-colors"
              >
                <FontAwesomeIcon icon={faArrowsRotate} className="text-sm" />
                <span>Organize</span>
              </button>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-4">
          {/* Search Bar */}
          <div className="flex-1 relative">
            <FontAwesomeIcon 
              icon={faSearch} 
              className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" 
            />
            <input 
              type="text" 
              placeholder="Search contexts..." 
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full bg-gray-800 border border-tertiary/50 rounded-lg pl-12 pr-4 py-3 text-supplement1 placeholder-gray-400 focus:outline-none focus:border-primary/50"
            />
          </div>

          {/* View Toggle */}
          <div className="flex items-center bg-gray-800 rounded-lg p-1 border border-tertiary/50">
            <button 
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md ${viewMode === 'grid' ? 'bg-primary/20 text-primary' : 'text-gray-400 hover:text-supplement1'}`}
            >
              <FontAwesomeIcon icon={faGrip} className="text-sm" />
            </button>
            <button 
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md ${viewMode === 'list' ? 'bg-primary/20 text-primary' : 'text-gray-400 hover:text-supplement1'}`}
            >
              <FontAwesomeIcon icon={faList} className="text-sm" />
            </button>
          </div>

          {/* Sort Toggle */}
          <button className="flex items-center gap-2 px-4 py-3 bg-gray-800 border border-tertiary/50 rounded-lg text-supplement1 hover:bg-gray-700 transition-colors">
            <FontAwesomeIcon icon={faSort} className="text-sm" />
            <span className="text-sm">{sortBy}</span>
          </button>
        </div>
      </div>

      {/* Context Cards Grid */}
      <div className="flex-1 p-6 overflow-y-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {contexts.map((context) => {
            const colors = getColorClasses(context.color)
            return (
              <div 
                key={context.id}
                className={`context-card p-4 rounded-lg border ${colors.border} cursor-pointer transition-all hover:bg-gray-800/50 bg-gradient-to-br ${colors.gradient} hover:transform hover:-translate-y-1`}
                onClick={() => handleContextSelect(context)}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm truncate text-supplement1">{context.name}</h4>
                    <p className={`text-xs ${colors.text}/80`}>{context.category}</p>
                  </div>
                  <div className="flex items-center gap-1 ml-2">
                    <FontAwesomeIcon icon={context.icon} className={`${colors.text} text-sm`} />
                    <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
                      <FontAwesomeIcon icon={faInfoCircle} className="text-gray-400 text-xs" />
                      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        Details
                      </div>
                    </button>
                  </div>
                </div>
                <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
                  <span className={colors.text}>{context.chatCount} chats</span>
                  <span className={colors.text}>{context.fileCount} files</span>
                </div>
                <p className="text-xs text-gray-400 line-clamp-2">{context.description}</p>
                <div className="flex items-center justify-between mt-3">
                  <div className="flex items-center gap-1">
                    <div className={`w-2 h-2 ${colors.dot} rounded-full`}></div>
                    <span className="text-xs text-gray-400">Updated {context.lastUpdated}</span>
                  </div>
                  <button 
                    onClick={(e) => {
                      e.stopPropagation()
                      handleContextView(context.id)
                    }}
                    className={`px-3 py-1 ${colors.button} rounded-md text-xs font-medium transition-colors`}
                  >
                    View
                  </button>
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Context Details Modal */}
      {selectedContext && (
        <div className="fixed inset-0 bg-gray-800/80 backdrop-blur-sm z-50" onClick={closeContextDetails}>
          <div className="flex items-center justify-center min-h-screen p-6">
            <div className="bg-gray-800 rounded-xl border border-tertiary/50 w-full max-w-6xl max-h-[80vh] overflow-hidden" onClick={(e) => e.stopPropagation()}>
              {/* Details Header */}
              <div className="flex items-center justify-between p-6 border-b border-tertiary/50">
                <div className="flex items-center gap-3">
                  <FontAwesomeIcon icon={selectedContext.icon} className="text-supplement2 text-lg" />
                  <div>
                    <h2 className="text-lg font-semibold text-supplement1">{selectedContext.name}</h2>
                    <p className="text-sm text-gray-400">{selectedContext.category} Context</p>
                  </div>
                </div>
                <button 
                  className="p-2 hover:bg-gray-700 rounded-lg transition-colors" 
                  onClick={closeContextDetails}
                >
                  <FontAwesomeIcon icon={faXmark} className="text-gray-400" />
                </button>
              </div>

              {/* Details Content */}
              <div className="flex h-[60vh]">
                {/* Recent Chats - 30% */}
                <div className="w-[30%] border-r border-tertiary/50 flex flex-col">
                  <div className="p-4 border-b border-tertiary/50">
                    <h3 className="font-medium text-supplement1 mb-2">Recent Chats</h3>
                  </div>
                  <div className="flex-1 overflow-y-auto p-4 space-y-3">
                    <div className="p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 cursor-pointer transition-colors">
                      <p className="text-sm font-medium text-supplement1 truncate">Component Architecture</p>
                      <p className="text-xs text-gray-400 mt-1">2 hours ago</p>
                    </div>
                    <div className="p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 cursor-pointer transition-colors">
                      <p className="text-sm font-medium text-supplement1 truncate">Color Token Updates</p>
                      <p className="text-xs text-gray-400 mt-1">1 day ago</p>
                    </div>
                  </div>
                </div>

                {/* Files - 30% */}
                <div className="w-[30%] border-r border-tertiary/50 flex flex-col">
                  <div className="p-4 border-b border-tertiary/50">
                    <h3 className="font-medium text-supplement1 mb-2">Files</h3>
                  </div>
                  <div className="flex-1 overflow-y-auto p-4 space-y-3">
                    <div className="flex items-center gap-3 p-2 hover:bg-gray-700/50 rounded cursor-pointer">
                      <FontAwesomeIcon icon={faFileCode} className="text-supplement2" />
                      <div className="flex-1">
                        <p className="text-sm text-supplement1 truncate">tokens.json</p>
                        <p className="text-xs text-gray-400">Design tokens</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-2 hover:bg-gray-700/50 rounded cursor-pointer">
                      <FontAwesomeIcon icon={faImage} className="text-supplement2" />
                      <div className="flex-1">
                        <p className="text-sm text-supplement1 truncate">components.fig</p>
                        <p className="text-xs text-gray-400">Figma file</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Master.md Preview - 40% */}
                <div className="w-[40%] flex flex-col">
                  <div className="p-4 border-b border-tertiary/50">
                    <h3 className="font-medium text-supplement1 mb-2">Master.md Preview</h3>
                  </div>
                  <div className="flex-1 overflow-y-auto p-4">
                    <div className="prose prose-invert prose-sm max-w-none">
                      <h1 className="text-supplement1">{selectedContext.name}</h1>
                      <p className="text-gray-300">{selectedContext.description}</p>
                      <h2 className="text-supplement1">Overview</h2>
                      <p className="text-gray-300">This context contains {selectedContext.chatCount} conversations and {selectedContext.fileCount} files related to {selectedContext.category.toLowerCase()}.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default HomePage
